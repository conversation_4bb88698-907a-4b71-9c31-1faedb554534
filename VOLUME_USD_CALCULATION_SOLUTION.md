# Giải pháp tính toán Volume USD cho Affiliate Transactions

## Vấn đề đã được gi<PERSON>i quyết ✅

Tại bảng `affiliate_transactions`, cột `volume_usd` trước đây đang được lấy cùng giá trị với cột `quote_amount`, nguyên nhân là do NATS Stream không thể trả về `volume_usd` ở Subject: `"agency.affiliate.xbit_tx"`.

**Vấn đề cũ:**
- `quote_amount` = số lượng SOL (ví dụ: 2.5 SOL)
- `volume_usd` = giá trị USD (ví dụ: $250 USD)
- Trước đây: `volume_usd = quote_amount` (sai)
- Đúng phải là: `volume_usd = quote_amount * sol_price_usd`

## Giải pháp mới đã triển khai ✅

### 1. NATS Stream đã được cập nhật với USD Rate fields

**Thay đổi chính:** NATS Stream `"agency.affiliate.xbit_tx"` giờ đây trả về 2 fields mới:
- `close_base_usd_rate`: Giá base token theo USD
- `close_quote_usd_rate`: Giá quote token theo USD (thường là SOL price)

**Ví dụ NATS payload:**
```json
{
  "items": [
    {
      "order_id": "636f75b2-5437-4b66-9ad4-1340c1d819f6",
      "quote_amount": "0.00009999",
      "quote_symbol": "SOL",
      "close_base_usd_rate": "8.4754519288509722",
      "close_quote_usd_rate": "233.8594940829458833",
      "status": "Completed"
    }
  ]
}
```

### 2. Cập nhật NATS Model

**File:** `internal/nats/model.go`

Thêm 2 fields mới vào `AffiliateTxEvent`:
```go
type AffiliateTxEvent struct {
    // ... existing fields ...

    // USD Rate fields for volume calculation
    CloseBaseUsdRate  decimal.Decimal `json:"close_base_usd_rate"`  // Base token price in USD
    CloseQuoteUsdRate decimal.Decimal `json:"close_quote_usd_rate"` // Quote token price in USD (usually SOL price)
}
```

### 3. Cải thiện logic tính toán Volume USD

**File:** `internal/service/affiliate/affiliate_service.go`

#### Thêm method `calculateVolumeUSD` mới:
```go
func (s *AffiliateService) calculateVolumeUSD(txEvent *natsModel.AffiliateTxEvent) decimal.Decimal {
    // Ưu tiên 1: Sử dụng close_quote_usd_rate từ NATS (chính xác nhất)
    if !txEvent.CloseQuoteUsdRate.IsZero() {
        return txEvent.QuoteAmount.Mul(txEvent.CloseQuoteUsdRate)
    }

    // Ưu tiên 2: Fallback sử dụng latestSolPrice từ memory
    s.priceMutex.RLock()
    latestPrice := s.latestSolPrice
    s.priceMutex.RUnlock()

    if !latestPrice.IsZero() {
        return txEvent.QuoteAmount.Mul(latestPrice)
    }

    // Ưu tiên 3: Final fallback (backward compatibility)
    return txEvent.QuoteAmount
}
```

#### Cập nhật cả `createNewTransaction` và `updateExistingTransaction`:
```go
// Thay thế logic cũ:
volumeUSD := txEvent.QuoteAmount.Mul(s.latestSolPrice)
// hoặc: existingTx.VolumeUSD = txEvent.QuoteAmount

// Bằng logic mới:
volumeUSD := s.calculateVolumeUSD(txEvent)
```

### 4. Cập nhật các chỗ sử dụng volume calculation cũ

**File:** `internal/task/level/level_upgrade.go`

Thay thế logic tính toán volume cũ:
```go
// Cũ: Tính toán volume bằng quote_amount * latestSolPrice
err = global.GVA_DB.Model(&model.AffiliateTransaction{}).
    Select(`
        user_id,
        COALESCE(SUM(quote_amount * ?), 0) as total_volume,
        COUNT(*) as transaction_count
    `, latestSolPrice.InexactFloat64()).
    // ...

// Mới: Sử dụng trực tiếp volume_usd đã được tính toán chính xác
err := global.GVA_DB.Model(&model.AffiliateTransaction{}).
    Select(`
        user_id,
        COALESCE(SUM(volume_usd), 0) as total_volume,
        COUNT(*) as transaction_count
    `).
    // ...
```

## Luồng hoạt động mới

#### Khi nhận affiliate transaction từ NATS:
1. Subject: `"agency.affiliate.xbit_tx"`
2. Parse NATS payload với 2 fields mới: `close_base_usd_rate`, `close_quote_usd_rate`
3. `calculateVolumeUSD()` được gọi với logic ưu tiên:
   - **Ưu tiên 1:** Sử dụng `close_quote_usd_rate` từ NATS (chính xác nhất)
   - **Ưu tiên 2:** Fallback sử dụng `latestSolPrice` từ memory
   - **Ưu tiên 3:** Final fallback sử dụng `quote_amount` (backward compatibility)
4. `volume_usd = quote_amount * close_quote_usd_rate`

## Ưu điểm của giải pháp mới

1. **Tính toán chính xác:** Sử dụng giá USD chính xác từ NATS thay vì ước tính
2. **Real-time:** Giá USD được cập nhật theo từng transaction
3. **Đơn giản:** Không cần quản lý SOL price riêng biệt
4. **Hiệu suất cao:** Không cần query database để lấy giá
5. **Backward compatibility:** Vẫn có fallback mechanism
6. **Consistency:** Tất cả volume calculations đều sử dụng cùng một nguồn dữ liệu

## Cấu trúc dữ liệu mới

#### NATS Subject: `"agency.affiliate.xbit_tx"`
```json
{
  "quote_amount": "0.00009999",           // Số lượng SOL
  "quote_symbol": "SOL",                  // Symbol
  "close_base_usd_rate": "8.475...",      // Giá base token (USD)
  "close_quote_usd_rate": "233.859..."    // Giá quote token (USD) - SOL price
}
```

#### Database Table: `affiliate_transactions`
- `quote_amount`: decimal (SOL amount)
- `volume_usd`: decimal (USD value) = `quote_amount * close_quote_usd_rate`

## Ví dụ tính toán mới

```
Input từ NATS:
- quote_amount = 0.00009999 SOL
- close_quote_usd_rate = 233.8594940829458833 USD/SOL

Calculation:
- volume_usd = 0.00009999 * 233.8594940829458833 = 0.02338 USD

Result:
- affiliate_transactions.quote_amount = 0.00009999
- affiliate_transactions.volume_usd = 0.02338
```

## Kết luận

Giải pháp mới này đảm bảo `volume_usd` được tính toán chính xác và đơn giản hơn bằng cách sử dụng trực tiếp USD rate từ NATS, loại bỏ sự phụ thuộc vào SOL price management riêng biệt.
