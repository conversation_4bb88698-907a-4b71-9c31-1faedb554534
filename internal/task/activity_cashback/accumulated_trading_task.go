package activity_cashback

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
	"go.uber.org/zap"
)

// AccumulatedTradingTask handles accumulated trading volume calculations and task updates
type AccumulatedTradingTask struct {
	service activity_cashback.ActivityCashbackServiceInterface
}

// NewAccumulatedTradingTask creates a new AccumulatedTradingTask instance
func NewAccumulatedTradingTask() *AccumulatedTradingTask {
	return &AccumulatedTradingTask{
		service: activity_cashback.NewActivityCashbackService(),
	}
}

// ProcessAccumulatedTradingVolumes processes accumulated trading volumes for all users
// This job runs every 15 minutes to update user_tier_info.trading_volume_usd and check accumulated tasks
func (t *AccumulatedTradingTask) ProcessAccumulatedTradingVolumes() {
	global.GVA_LOG.Info("Starting accumulated trading volumes processing job")

	ctx := context.Background()

	// Step 1: Get users who have new trading activity since last processing
	usersToProcess, err := t.GetUsersWithNewTradingActivity(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get users with new trading activity", zap.Error(err))
		return
	}

	if len(usersToProcess) == 0 {
		global.GVA_LOG.Info("No users with new trading activity found")
		return
	}

	global.GVA_LOG.Info("Found users with new trading activity",
		zap.Int("user_count", len(usersToProcess)))

	// Step 2: Process each user's accumulated volume
	processedCount := 0
	for _, userID := range usersToProcess {
		if err := t.processUserAccumulatedVolume(ctx, userID); err != nil {
			global.GVA_LOG.Error("Failed to process user accumulated volume",
				zap.String("user_id", userID.String()),
				zap.Error(err))
			continue
		}
		processedCount++
	}

	global.GVA_LOG.Info("Accumulated trading volumes processing completed",
		zap.Int("total_users", len(usersToProcess)),
		zap.Int("processed_users", processedCount))
}

// GetUsersWithNewTradingActivity gets users who have new trading activity since last processing
func (t *AccumulatedTradingTask) GetUsersWithNewTradingActivity(ctx context.Context) ([]uuid.UUID, error) {
	// Get users who have affiliate transactions created/updated in the last 20 minutes
	// (slightly more than 15 minutes to account for processing delays)
	since := time.Now().UTC().Add(-20 * time.Minute)

	var userIDs []uuid.UUID

	// Query 1: Users with new/updated affiliate transactions
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.AffiliateTransaction{}).
		Select("DISTINCT user_id").
		Where("user_id IS NOT NULL").
		Where("status = ?", "Completed").
		Where("updated_at >= ?", since).
		Pluck("user_id", &userIDs).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get users with new trading activity: %w", err)
	}

	// Query 2: Also check users whose user_tier_info.trading_volume_usd might be stale
	// This handles cases where volume calculation might have been missed
	var staleUserIDs []uuid.UUID
	err = global.GVA_DB.WithContext(ctx).Raw(`
		SELECT DISTINCT at.user_id
		FROM affiliate_transactions at
		LEFT JOIN user_tier_info uti ON at.user_id = uti.user_id
		WHERE at.user_id IS NOT NULL
		AND at.status = 'Completed'
		AND (
			uti.user_id IS NULL
			OR uti.updated_at < at.updated_at
			OR uti.updated_at < ?
		)
		LIMIT 100
	`, since).Pluck("user_id", &staleUserIDs).Error

	if err != nil {
		global.GVA_LOG.Warn("Failed to get users with stale tier info", zap.Error(err))
	} else {
		// Merge with existing user IDs
		userIDMap := make(map[uuid.UUID]bool)
		for _, id := range userIDs {
			userIDMap[id] = true
		}
		for _, id := range staleUserIDs {
			if !userIDMap[id] {
				userIDs = append(userIDs, id)
				userIDMap[id] = true
			}
		}
	}

	return userIDs, nil
}

// ProcessUserAccumulatedVolume processes accumulated volume for a single user
func (t *AccumulatedTradingTask) ProcessUserAccumulatedVolume(ctx context.Context, userID uuid.UUID) error {
	// Step 1: Calculate user's total accumulated MEME volume from affiliate_transactions
	totalVolume, err := t.calculateUserTotalMemeVolume(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to calculate user total MEME volume: %w", err)
	}

	// Step 2: Update user_tier_info.trading_volume_usd
	if err := t.updateUserTierInfoTradingVolume(ctx, userID, totalVolume); err != nil {
		return fmt.Errorf("failed to update user tier info trading volume: %w", err)
	}

	// Step 3: Check and update accumulated trading tasks
	if err := t.checkAndUpdateAccumulatedTasks(ctx, userID, totalVolume); err != nil {
		return fmt.Errorf("failed to check accumulated tasks: %w", err)
	}

	global.GVA_LOG.Debug("User accumulated volume processed successfully",
		zap.String("user_id", userID.String()),
		zap.String("total_volume", totalVolume.String()))

	return nil
}

// calculateUserTotalMemeVolume calculates user's total MEME trading volume from affiliate_transactions
// This uses a more efficient approach by leveraging database indexes
func (t *AccumulatedTradingTask) calculateUserTotalMemeVolume(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	var totalVolume decimal.Decimal

	// Use optimized query with proper indexing
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.AffiliateTransaction{}).
		Select("COALESCE(SUM(volume_usd), 0)").
		Where("user_id = ? AND status = ?", userID, "Completed").
		Scan(&totalVolume).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to calculate total MEME volume: %w", err)
	}

	global.GVA_LOG.Debug("Calculated user total MEME volume",
		zap.String("user_id", userID.String()),
		zap.String("total_volume", totalVolume.String()))

	return totalVolume, nil
}

// updateUserTierInfoTradingVolume updates user_tier_info.trading_volume_usd
func (t *AccumulatedTradingTask) updateUserTierInfoTradingVolume(ctx context.Context, userID uuid.UUID, totalVolume decimal.Decimal) error {
	// Use a more efficient approach: try to update first, then create if not exists
	result := global.GVA_DB.WithContext(ctx).
		Model(&model.UserTierInfo{}).
		Where("user_id = ?", userID).
		Update("trading_volume_usd", totalVolume)

	if result.Error != nil {
		return fmt.Errorf("failed to update trading volume: %w", result.Error)
	}

	// If no rows were affected, the record doesn't exist, so create it
	if result.RowsAffected == 0 {
		// Get or create user tier info through service (which handles creation)
		_, err := t.service.GetUserTierInfo(ctx, userID)
		if err != nil {
			return fmt.Errorf("failed to get/create user tier info: %w", err)
		}

		// Update the newly created record
		err = global.GVA_DB.WithContext(ctx).
			Model(&model.UserTierInfo{}).
			Where("user_id = ?", userID).
			Update("trading_volume_usd", totalVolume).Error

		if err != nil {
			return fmt.Errorf("failed to update trading volume after creation: %w", err)
		}

		global.GVA_LOG.Debug("Created and updated user tier info trading volume",
			zap.String("user_id", userID.String()),
			zap.String("new_volume", totalVolume.String()))
	} else {
		global.GVA_LOG.Debug("Updated user tier info trading volume",
			zap.String("user_id", userID.String()),
			zap.String("new_volume", totalVolume.String()))
	}

	return nil
}

// checkAndUpdateAccumulatedTasks checks and updates accumulated trading tasks for a user
func (t *AccumulatedTradingTask) checkAndUpdateAccumulatedTasks(ctx context.Context, userID uuid.UUID, accumulatedVolume decimal.Decimal) error {
	// Define accumulated trading tasks with their milestones (in USD)
	accumulatedTasks := []struct {
		identifier model.TaskIdentifier
		milestone  float64 // in USD
	}{
		{model.TaskIDAccumulatedTrading10K, 10000},
		{model.TaskIDAccumulatedTrading50K, 50000},
		{model.TaskIDAccumulatedTrading100K, 100000},
		{model.TaskIDAccumulatedTrading500K, 500000},
	}

	// Convert accumulated volume to float64 for comparison
	volumeFloat, _ := accumulatedVolume.Float64()

	// Get all trading tasks
	tradingTasks, err := t.service.GetTasksByCategory(ctx, model.CategoryTrading)
	if err != nil {
		return fmt.Errorf("failed to get trading tasks: %w", err)
	}

	// Create task map for quick lookup
	taskMap := make(map[model.TaskIdentifier]model.ActivityTask)
	for _, task := range tradingTasks {
		if task.TaskIdentifier != nil {
			taskMap[*task.TaskIdentifier] = task
		}
	}

	// Process each accumulated task
	for _, taskData := range accumulatedTasks {
		task, exists := taskMap[taskData.identifier]
		if !exists {
			continue
		}

		// Get current progress
		progress, err := t.service.GetTaskProgress(ctx, userID, task.ID)
		if err != nil {
			global.GVA_LOG.Debug("Failed to get task progress",
				zap.String("task_id", task.ID.String()),
				zap.Error(err))
			continue
		}

		// Skip if already completed
		if progress.IsCompleted() {
			continue
		}

		// Update progress with current accumulated volume (but don't exceed milestone)
		progressValue := int(volumeFloat)
		if volumeFloat >= taskData.milestone {
			progressValue = int(taskData.milestone)
		}

		// Update progress
		if err := t.service.SetProgress(ctx, userID, task.ID, progressValue); err != nil {
			global.GVA_LOG.Error("Failed to update accumulated task progress",
				zap.String("task_id", task.ID.String()),
				zap.Error(err))
			continue
		}

		// Check if milestone reached and complete task
		if volumeFloat >= taskData.milestone {
			verificationData := map[string]interface{}{
				"milestone":          taskData.milestone,
				"accumulated_volume": volumeFloat,
				"method":             "scheduled_accumulated_task",
				"processor":          "AccumulatedTradingTask",
				"source":             "affiliate_transactions_sum",
			}

			if err := t.service.CompleteTaskWithPoints(ctx, userID, task.ID, verificationData); err != nil {
				global.GVA_LOG.Error("Failed to complete accumulated task",
					zap.String("task_id", task.ID.String()),
					zap.Error(err))
				continue
			}

			global.GVA_LOG.Info("Accumulated trading milestone reached",
				zap.String("user_id", userID.String()),
				zap.String("task_identifier", string(taskData.identifier)),
				zap.Float64("milestone", taskData.milestone),
				zap.Float64("accumulated_volume", volumeFloat),
				zap.Int("points", task.Points))
		}
	}

	return nil
}
