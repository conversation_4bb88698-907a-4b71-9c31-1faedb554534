package affiliate

import (
	"sync"
	"testing"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	natsModel "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
)

func TestAffiliateService_calculateVolumeUSD(t *testing.T) {
	service := &AffiliateService{
		latestSolPrice: decimal.NewFromFloat(200.0), // Fallback SOL price
		priceMutex:     sync.RWMutex{},
	}

	tests := []struct {
		name     string
		txEvent  *natsModel.AffiliateTxEvent
		expected decimal.Decimal
	}{
		{
			name: "should use close_quote_usd_rate from NATS when available",
			txEvent: &natsModel.AffiliateTxEvent{
				ID:                uuid.New(),
				QuoteAmount:       decimal.NewFromFloat(0.00009999),           // 0.00009999 SOL
				CloseQuoteUsdRate: decimal.NewFromFloat(233.8594940829458833), // SOL price from NATS
			},
			expected: decimal.NewFromFloat(0.00009999).Mul(decimal.NewFromFloat(233.8594940829458833)), // ~0.02338 USD
		},
		{
			name: "should fallback to latestSolPrice when close_quote_usd_rate is zero",
			txEvent: &natsModel.AffiliateTxEvent{
				ID:                uuid.New(),
				QuoteAmount:       decimal.NewFromFloat(2.5), // 2.5 SOL
				CloseQuoteUsdRate: decimal.Zero,              // No USD rate from NATS
			},
			expected: decimal.NewFromFloat(2.5).Mul(decimal.NewFromFloat(200.0)), // 2.5 * 200 = 500 USD
		},
		{
			name: "should use quote_amount as final fallback when no prices available",
			txEvent: &natsModel.AffiliateTxEvent{
				ID:                uuid.New(),
				QuoteAmount:       decimal.NewFromFloat(1.5), // 1.5 SOL
				CloseQuoteUsdRate: decimal.Zero,              // No USD rate from NATS
			},
			expected: decimal.NewFromFloat(1.5), // Final fallback: use quote_amount directly
		},
		{
			name: "should handle zero quote_amount",
			txEvent: &natsModel.AffiliateTxEvent{
				ID:                uuid.New(),
				QuoteAmount:       decimal.Zero,
				CloseQuoteUsdRate: decimal.NewFromFloat(233.85),
			},
			expected: decimal.Zero, // 0 * 233.85 = 0
		},
		{
			name: "should handle very small amounts accurately",
			txEvent: &natsModel.AffiliateTxEvent{
				ID:                uuid.New(),
				QuoteAmount:       decimal.NewFromFloat(0.000001),      // 0.000001 SOL
				CloseQuoteUsdRate: decimal.NewFromFloat(250.123456789), // High precision SOL price
			},
			expected: decimal.NewFromFloat(0.000001).Mul(decimal.NewFromFloat(250.123456789)), // ~0.000250123456789 USD
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// For the fallback test, we need to set latestSolPrice to zero
			if tt.name == "should use quote_amount as final fallback when no prices available" {
				service.latestSolPrice = decimal.Zero
			} else {
				service.latestSolPrice = decimal.NewFromFloat(200.0)
			}

			result := service.calculateVolumeUSD(tt.txEvent)

			// Use a small tolerance for decimal comparison due to floating point precision
			tolerance := decimal.NewFromFloat(0.000000001)
			diff := result.Sub(tt.expected).Abs()

			assert.True(t, diff.LessThanOrEqual(tolerance),
				"Expected %s, got %s, diff: %s",
				tt.expected.String(), result.String(), diff.String())
		})
	}
}

func TestAffiliateService_calculateVolumeUSD_RealWorldExample(t *testing.T) {
	service := &AffiliateService{
		priceMutex: sync.RWMutex{},
	}

	// Real-world example from the NATS payload provided by user
	txEvent := &natsModel.AffiliateTxEvent{
		ID:                uuid.MustParse("636f75b2-5437-4b66-9ad4-1340c1d819f6"),
		QuoteAmount:       decimal.NewFromFloat(0.00009999),           // 0.00009999 SOL
		CloseQuoteUsdRate: decimal.NewFromFloat(233.8594940829458833), // SOL price in USD
	}

	result := service.calculateVolumeUSD(txEvent)

	// Expected: 0.00009999 * 233.8594940829458833 ≈ 0.02338
	expected := decimal.NewFromFloat(0.00009999).Mul(decimal.NewFromFloat(233.8594940829458833))

	tolerance := decimal.NewFromFloat(0.000001) // 1 micro USD tolerance
	diff := result.Sub(expected).Abs()

	assert.True(t, diff.LessThanOrEqual(tolerance),
		"Real-world calculation failed. Expected %s, got %s, diff: %s",
		expected.String(), result.String(), diff.String())

	// Verify the result is reasonable (should be around 0.02338 USD)
	assert.True(t, result.GreaterThan(decimal.NewFromFloat(0.02)),
		"Result should be greater than 0.02 USD")
	assert.True(t, result.LessThan(decimal.NewFromFloat(0.025)),
		"Result should be less than 0.025 USD")

	t.Logf("✅ Real-world volume calculation: %s SOL * %s USD/SOL = %s USD",
		txEvent.QuoteAmount.String(),
		txEvent.CloseQuoteUsdRate.String(),
		result.String())
}

func TestAffiliateService_calculateVolumeUSD_EdgeCases(t *testing.T) {
	service := &AffiliateService{
		latestSolPrice: decimal.NewFromFloat(150.0),
		priceMutex:     sync.RWMutex{},
	}

	t.Run("negative quote amount should still calculate correctly", func(t *testing.T) {
		txEvent := &natsModel.AffiliateTxEvent{
			ID:                uuid.New(),
			QuoteAmount:       decimal.NewFromFloat(-1.0), // Negative amount (shouldn't happen but test anyway)
			CloseQuoteUsdRate: decimal.NewFromFloat(200.0),
		}

		result := service.calculateVolumeUSD(txEvent)
		expected := decimal.NewFromFloat(-200.0) // -1.0 * 200.0

		assert.True(t, result.Equal(expected),
			"Expected %s, got %s", expected.String(), result.String())
	})

	t.Run("very large amounts should be handled correctly", func(t *testing.T) {
		txEvent := &natsModel.AffiliateTxEvent{
			ID:                uuid.New(),
			QuoteAmount:       decimal.NewFromFloat(1000000.0), // 1M SOL
			CloseQuoteUsdRate: decimal.NewFromFloat(300.0),     // $300/SOL
		}

		result := service.calculateVolumeUSD(txEvent)
		expected := decimal.NewFromFloat(300000000.0) // 1M * 300 = 300M USD

		assert.True(t, result.Equal(expected),
			"Expected %s, got %s", expected.String(), result.String())
	})
}
