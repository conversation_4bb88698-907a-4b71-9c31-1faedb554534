package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
	"go.uber.org/zap"
)

func main() {
	// Initialize the application
	global.GVA_VP = initializer.Viper()
	global.GVA_LOG = initializer.Zap()
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initializer.Gorm()
	initializer.DBList()

	ctx := context.Background()

	// Create activity cashback service
	service := activity_cashback.NewActivityCashbackService()

	// Create a test user
	testUserID := uuid.New()
	fmt.Printf("Testing with user ID: %s\n", testUserID.String())

	// Initialize user for activity cashback
	if err := service.InitializeUserForActivityCashback(ctx, testUserID); err != nil {
		log.Fatalf("Failed to initialize user: %v", err)
	}

	// Get Share Earnings Chart task
	tasks, err := service.GetTasksByCategory(ctx, model.CategoryCommunity)
	if err != nil {
		log.Fatalf("Failed to get community tasks: %v", err)
	}

	var shareEarningsTask *model.ActivityTask
	for _, task := range tasks {
		if task.TaskIdentifier != nil && *task.TaskIdentifier == model.TaskIDShareEarningsChart {
			shareEarningsTask = &task
			break
		}
	}

	if shareEarningsTask == nil {
		log.Fatalf("Share Earnings Chart task not found")
	}

	fmt.Printf("Found task: %s (ID: %s)\n", shareEarningsTask.Name, shareEarningsTask.ID.String())
	fmt.Printf("Task frequency: %s\n", shareEarningsTask.Frequency)
	fmt.Printf("Task points: %d\n", shareEarningsTask.Points)

	// Create task registry and get handler
	registry := activity_cashback.NewTaskRegistry(service)
	handler, exists := registry.GetHandler(model.TaskIDShareEarningsChart)
	if !exists {
		log.Fatalf("Handler not found for Share Earnings Chart task")
	}

	fmt.Printf("Handler found: %s\n", handler.GetIdentifier())

	// Test multiple completions
	fmt.Println("\n=== Testing Multiple Completions ===")

	// Get initial user tier info
	initialTierInfo, err := service.GetUserTierInfo(ctx, testUserID)
	if err != nil {
		log.Fatalf("Failed to get initial tier info: %v", err)
	}
	fmt.Printf("Initial points: %d\n", initialTierInfo.TotalPoints)

	// First completion - should award points
	fmt.Println("\n--- First Completion ---")
	err = handler.Handle(ctx, testUserID, shareEarningsTask, map[string]interface{}{
		"action": "share_earnings_chart",
	})
	if err != nil {
		log.Printf("First completion error: %v", err)
	} else {
		fmt.Println("First completion successful")
	}

	// Check points after first completion
	tierInfo1, err := service.GetUserTierInfo(ctx, testUserID)
	if err != nil {
		log.Printf("Failed to get tier info after first completion: %v", err)
	} else {
		fmt.Printf("Points after first completion: %d (gained: %d)\n",
			tierInfo1.TotalPoints, tierInfo1.TotalPoints-initialTierInfo.TotalPoints)
	}

	// Wait a moment
	time.Sleep(1 * time.Second)

	// Second completion - should not award points
	fmt.Println("\n--- Second Completion ---")
	err = handler.Handle(ctx, testUserID, shareEarningsTask, map[string]interface{}{
		"action": "share_earnings_chart",
	})
	if err != nil {
		log.Printf("Second completion error: %v", err)
	} else {
		fmt.Println("Second completion successful")
	}

	// Check points after second completion
	tierInfo2, err := service.GetUserTierInfo(ctx, testUserID)
	if err != nil {
		log.Printf("Failed to get tier info after second completion: %v", err)
	} else {
		fmt.Printf("Points after second completion: %d (gained: %d)\n",
			tierInfo2.TotalPoints, tierInfo2.TotalPoints-tierInfo1.TotalPoints)
	}

	// Third completion - should not award points
	fmt.Println("\n--- Third Completion ---")
	err = handler.Handle(ctx, testUserID, shareEarningsTask, map[string]interface{}{
		"action": "share_earnings_chart",
	})
	if err != nil {
		log.Printf("Third completion error: %v", err)
	} else {
		fmt.Println("Third completion successful")
	}

	// Check points after third completion
	tierInfo3, err := service.GetUserTierInfo(ctx, testUserID)
	if err != nil {
		log.Printf("Failed to get tier info after third completion: %v", err)
	} else {
		fmt.Printf("Points after third completion: %d (gained: %d)\n",
			tierInfo3.TotalPoints, tierInfo3.TotalPoints-tierInfo2.TotalPoints)
	}

	// Check task progress
	fmt.Println("\n=== Task Progress ===")
	progress, err := service.GetTaskProgress(ctx, testUserID, shareEarningsTask.ID)
	if err != nil {
		log.Printf("Failed to get task progress: %v", err)
	} else {
		fmt.Printf("Progress value: %d\n", progress.ProgressValue)
		fmt.Printf("Completion count: %d\n", progress.CompletionCount)
		fmt.Printf("Status: %s\n", progress.Status)
		if progress.LastCompletedAt != nil {
			fmt.Printf("Last completed at: %s\n", progress.LastCompletedAt.Format(time.RFC3339))
		}
	}

	fmt.Println("\n=== Test Summary ===")
	fmt.Printf("Total points gained: %d\n", tierInfo3.TotalPoints-initialTierInfo.TotalPoints)
	fmt.Printf("Expected: 10 points (only first completion should award points)\n")

	if tierInfo3.TotalPoints-initialTierInfo.TotalPoints == 10 {
		fmt.Println("✅ Test PASSED: Only first completion awarded points")
	} else {
		fmt.Println("❌ Test FAILED: Incorrect points awarded")
	}
}
