# SHARE_EARNINGS_CHART Task Fix Verification

## Problem Summary
The SHARE_EARNINGS_CHART task was designed to be "completed multiple times, but points awarded only once per day". However, after the first completion, the task status became `CLAIMED`, causing the frontend to disable the button.

## Root Cause
- GraphQL `CompleteTask` mutation used `TaskManagementService.CompleteTask`
- This method calls `CompleteProgress` which sets status to `CLAIMED`
- The existing `ShareEarningsChartHandler` was not being used

## Solution Implemented

### 1. Added Task Registry Integration to ActivityCashbackService
```go
// Added to ActivityCashbackService struct
taskRegistry *TaskRegistry

// Added method
func (s *ActivityCashbackService) ProcessTaskWithRegistry(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
    if s.taskRegistry == nil {
        return fmt.Errorf("task registry not initialized")
    }
    return s.taskRegistry.ProcessTask(ctx, userID, task, data)
}
```

### 2. Updated GraphQL Resolver Logic
```go
// In CompleteTask resolver
if task.TaskIdentifier != nil && *task.TaskIdentifier == model.TaskIDShareEarningsChart {
    // Use task registry for SHARE_EARNINGS_CHART to allow multiple completions per day
    if err := service.ProcessTaskWithRegistry(ctx, userUUID, task, verificationData); err != nil {
        // handle error
    }
} else {
    // Use traditional completion for other tasks
    if err := service.CompleteTask(ctx, userUUID, taskUUID, verificationData); err != nil {
        // handle error
    }
}
```

### 3. ShareEarningsChartHandler Logic (Already Existed)
```go
func (h *ShareEarningsChartHandler) Handle(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
    // Always allow task completion (increment progress)
    if err := h.service.IncrementProgress(ctx, userID, task.ID, 1); err != nil {
        return fmt.Errorf("failed to increment share earnings chart progress: %w", err)
    }

    // Check if user has already received points today
    hasReceivedPointsToday, err := h.hasReceivedPointsToday(ctx, userID, task.ID)
    if err != nil {
        // Continue without awarding points to be safe
        return nil
    }

    // If not received points today, award points
    if !hasReceivedPointsToday {
        // Award points and create daily completion record
        // ... point awarding logic
    }

    return nil
}
```

## Expected Behavior After Fix

### Task Status Flow:
1. **First completion**: Status = `IN_PROGRESS` or `COMPLETED` (not `CLAIMED`)
2. **Subsequent completions**: Status remains the same, progress increments
3. **Points**: Only awarded once per day via `daily_task_completions` table

### Frontend Behavior:
- ✅ Button remains enabled after first completion
- ✅ User can click multiple times per day
- ✅ Progress counter increases with each click
- ✅ Points only awarded once per day

## Key Differences from Other Tasks

| Aspect | Regular Tasks | SHARE_EARNINGS_CHART |
|--------|---------------|---------------------|
| Completion Method | `CompleteTask` → `CompleteProgress` | `ProcessTaskWithRegistry` → `IncrementProgress` |
| Status After Completion | `CLAIMED` | `IN_PROGRESS` or `COMPLETED` |
| Multiple Completions | Not allowed | Allowed |
| Point Awarding | Every completion | Once per day |
| Button State | Disabled after completion | Remains enabled |

## Testing Recommendations

### Manual Testing:
1. Complete SHARE_EARNINGS_CHART task first time
   - Verify points are awarded
   - Verify button remains enabled
   - Verify status is not `CLAIMED`

2. Complete SHARE_EARNINGS_CHART task second time (same day)
   - Verify no points awarded
   - Verify progress increments
   - Verify button remains enabled

3. Complete SHARE_EARNINGS_CHART task next day
   - Verify points are awarded again
   - Verify progress increments

### GraphQL Testing:
```graphql
mutation {
  completeTask(input: {
    taskId: "SHARE_EARNINGS_CHART_TASK_ID"
    verificationData: "{\"action\": \"share_earnings_chart\"}"
  }) {
    success
    message
    pointsAwarded
    tierUpgraded
  }
}
```

## Files Modified:
1. `internal/service/activity_cashback/activity_cashback_service.go`
2. `internal/service/activity_cashback/interfaces.go`
3. `internal/controller/graphql/resolvers/activity_cashback.go`
4. `internal/service/activity_cashback/mock_activity_cashback_service.go`

## Backward Compatibility:
- ✅ All other tasks continue to work as before
- ✅ Only SHARE_EARNINGS_CHART task uses the new flow
- ✅ No database schema changes required
- ✅ Existing task handlers remain unchanged
