package main

import (
	"context"
	"fmt"
	"log"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
)

func main() {
	fmt.Println("=== Testing SHARE_EARNINGS_CHART Task Logic ===")

	// Create a mock service to test the logic
	service := activity_cashback.NewActivityCashbackService()
	ctx := context.Background()
	testUserID := uuid.New()

	fmt.Printf("Test User ID: %s\n", testUserID.String())

	// Create a test SHARE_EARNINGS_CHART task
	shareEarningsTask := &model.ActivityTask{
		ID:             uuid.New(),
		Name:           "Share Earnings Chart",
		TaskIdentifier: &[]model.TaskIdentifier{model.TaskIDShareEarningsChart}[0],
		Points:         10,
		Frequency:      model.FrequencyUnlimited,
		CategoryID:     1, // Assuming community category ID is 1
	}

	fmt.Printf("Task ID: %s\n", shareEarningsTask.ID.String())
	fmt.Printf("Task Identifier: %s\n", *shareEarningsTask.TaskIdentifier)
	fmt.Printf("Task Frequency: %s\n", shareEarningsTask.Frequency)

	// Test data for task completion
	testData := map[string]interface{}{
		"action": "share_earnings_chart",
	}

	fmt.Println("\n=== Testing Task Registry Processing ===")

	// Test 1: First completion - should award points
	fmt.Println("\n--- First Completion ---")
	err := service.ProcessTaskWithRegistry(ctx, testUserID, shareEarningsTask, testData)
	if err != nil {
		log.Printf("First completion error: %v", err)
	} else {
		fmt.Println("First completion successful")
	}

	// Test 2: Second completion - should not award points but should increment progress
	fmt.Println("\n--- Second Completion ---")
	err = service.ProcessTaskWithRegistry(ctx, testUserID, shareEarningsTask, testData)
	if err != nil {
		log.Printf("Second completion error: %v", err)
	} else {
		fmt.Println("Second completion successful")
	}

	// Test 3: Third completion - should not award points but should increment progress
	fmt.Println("\n--- Third Completion ---")
	err = service.ProcessTaskWithRegistry(ctx, testUserID, shareEarningsTask, testData)
	if err != nil {
		log.Printf("Third completion error: %v", err)
	} else {
		fmt.Println("Third completion successful")
	}

	fmt.Println("\n=== Test Completed ===")
	fmt.Println("Note: This test only checks if the task registry logic works without database connection.")
	fmt.Println("The actual point awarding and progress tracking would require a database connection.")
}
