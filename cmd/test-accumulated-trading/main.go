package main

import (
	"context"
	"flag"
	"fmt"
	"log"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/task/activity_cashback"
	"go.uber.org/zap"
)

func main() {
	var configFile = flag.String("config", "config.yaml", "Configuration file path")
	flag.Parse()

	// Initialize the application
	global.GVA_VP = initializer.Viper(*configFile)
	global.GVA_LOG = initializer.Zap()
	global.GVA_DB = initializer.GormPgSql()

	if global.GVA_DB == nil {
		log.Fatal("Failed to initialize database")
	}

	global.GVA_LOG.Info("Testing Accumulated Trading Task")

	// Create accumulated trading task instance
	accumulatedTask := activity_cashback.NewAccumulatedTradingTask()

	// Test the processing
	ctx := context.Background()
	
	global.GVA_LOG.Info("Starting accumulated trading task test")
	
	// Get users with recent trading activity
	users, err := accumulatedTask.GetUsersWithNewTradingActivity(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get users with new trading activity", zap.Error(err))
		return
	}

	global.GVA_LOG.Info("Found users with recent trading activity", 
		zap.Int("user_count", len(users)))

	if len(users) == 0 {
		global.GVA_LOG.Info("No users with recent trading activity found")
		return
	}

	// Process first few users for testing
	maxUsers := 5
	if len(users) < maxUsers {
		maxUsers = len(users)
	}

	for i := 0; i < maxUsers; i++ {
		userID := users[i]
		global.GVA_LOG.Info("Processing user", zap.String("user_id", userID.String()))
		
		if err := accumulatedTask.ProcessUserAccumulatedVolume(ctx, userID); err != nil {
			global.GVA_LOG.Error("Failed to process user accumulated volume",
				zap.String("user_id", userID.String()),
				zap.Error(err))
		} else {
			global.GVA_LOG.Info("Successfully processed user accumulated volume",
				zap.String("user_id", userID.String()))
		}
	}

	global.GVA_LOG.Info("Accumulated trading task test completed")
}
