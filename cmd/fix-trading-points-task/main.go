package main

import (
	"context"
	"flag"
	"log"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
)

func main() {
	var configPath string
	flag.StringVar(&configPath, "config", "config.yaml", "Path to config file")
	flag.Parse()

	// Initialize the application
	global.GVA_VP = initializer.Viper(configPath)
	global.GVA_LOG = initializer.Zap()
	global.GVA_DB = initializer.GormPgSql()

	if global.GVA_DB == nil {
		log.Fatal("Failed to initialize database connection")
	}

	global.GVA_LOG.Info("🔄 Starting Trading Points task frequency fix...")

	ctx := context.Background()

	// Find Trading Points task
	var task model.ActivityTask
	err := global.GVA_DB.WithContext(ctx).
		Where("task_identifier = ?", model.TaskIDTradingPoints).
		First(&task).Error

	if err != nil {
		log.Fatalf("❌ Failed to find Trading Points task: %v", err)
	}

	global.GVA_LOG.Info("Found Trading Points task",
		zap.String("task_id", task.ID.String()),
		zap.String("name", task.Name),
		zap.String("current_frequency", string(task.Frequency)),
		zap.String("current_reset_period", string(*task.ResetPeriod)))

	// Update frequency and reset period
	task.Frequency = model.FrequencyUnlimited
	resetPeriod := model.ResetNever
	task.ResetPeriod = &resetPeriod

	err = global.GVA_DB.WithContext(ctx).Save(&task).Error
	if err != nil {
		log.Fatalf("❌ Failed to update Trading Points task: %v", err)
	}

	global.GVA_LOG.Info("✅ Trading Points task updated successfully",
		zap.String("task_id", task.ID.String()),
		zap.String("name", task.Name),
		zap.String("new_frequency", string(task.Frequency)),
		zap.String("new_reset_period", string(*task.ResetPeriod)))

	// Also clear any existing daily completion records for this task to allow immediate re-completion
	result := global.GVA_DB.WithContext(ctx).
		Where("task_id = ?", task.ID).
		Delete(&model.DailyTaskCompletion{})

	if result.Error != nil {
		global.GVA_LOG.Error("Failed to clear daily completion records", zap.Error(result.Error))
	} else {
		global.GVA_LOG.Info("Cleared daily completion records",
			zap.String("task_id", task.ID.String()),
			zap.Int64("deleted_count", result.RowsAffected))
	}

	global.GVA_LOG.Info("🎉 Trading Points task frequency fix completed successfully!")
}
